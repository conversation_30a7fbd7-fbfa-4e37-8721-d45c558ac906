<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Product Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4B0082;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #3a0066;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Add Product Functionality Test</h1>
        <p>This page tests the add product functionality without requiring admin authentication.</p>
        
        <button class="test-button" onclick="testAddProduct()">Test Add Product</button>
        <button class="test-button" onclick="testValidation()">Test Form Validation</button>
        <button class="test-button" onclick="viewProducts()">View All Products</button>
        
        <div id="testResults"></div>
        
        <h3>Instructions:</h3>
        <ol>
            <li>Click "Test Add Product" to test adding a sample product</li>
            <li>Click "Test Form Validation" to test form validation</li>
            <li>Click "View All Products" to see all products in storage</li>
        </ol>
    </div>

    <!-- Include required scripts -->
    <script src="js/admin.js"></script>
    
    <script>
        // Initialize admin manager
        const adminManager = new AdminManager();
        
        function showResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function testAddProduct() {
            try {
                const testProduct = {
                    name: 'Test Product ' + Date.now(),
                    description: 'This is a test product created by the test function',
                    price: 29.99,
                    originalPrice: 39.99,
                    category: 'men',
                    subcategory: 'tops',
                    brand: 'VAITH',
                    sku: 'TEST-' + Date.now(),
                    stock: 10,
                    status: 'active',
                    images: ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop'],
                    sizes: ['S', 'M', 'L'],
                    colors: ['Black', 'White']
                };
                
                const result = adminManager.createProduct(testProduct);
                if (result) {
                    showResult(`✅ Product added successfully! ID: ${result.id}, Name: ${result.name}`, true);
                } else {
                    showResult('❌ Failed to add product', false);
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, false);
            }
        }
        
        function testValidation() {
            try {
                // Test with invalid data
                const invalidProduct = {
                    name: '',
                    price: -10,
                    sku: ''
                };
                
                const result = adminManager.createProduct(invalidProduct);
                if (result) {
                    showResult('⚠️ Validation test: Product was created with invalid data (this might be expected)', true);
                } else {
                    showResult('❌ Validation test failed', false);
                }
            } catch (error) {
                showResult(`✅ Validation working: ${error.message}`, true);
            }
        }
        
        function viewProducts() {
            try {
                const products = adminManager.getAllProducts();
                showResult(`📦 Total products in storage: ${products.length}`, true);
                
                if (products.length > 0) {
                    const lastProduct = products[products.length - 1];
                    showResult(`📝 Last product: ${lastProduct.name} (SKU: ${lastProduct.sku})`, true);
                }
            } catch (error) {
                showResult(`❌ Error viewing products: ${error.message}`, false);
            }
        }
        
        // Run initial test
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 Test page loaded successfully', true);
            viewProducts();
        });
    </script>
</body>
</html>
