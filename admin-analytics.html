<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics & Reports - VAITH Admin</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">Analytics & Reports</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Page Header -->
                <div class="page-header">
                    <h2 class="page-title">Analytics Dashboard</h2>
                    <p class="page-subtitle">Track your business performance and insights</p>
                </div>

                <!-- Time Period Selector -->
                <div style="margin-bottom: 2rem;">
                    <div style="display: flex; gap: 1rem; align-items: center;">
                        <label style="font-weight: 500; color: var(--text-color);">Time Period:</label>
                        <select id="timePeriod" class="form-select" style="padding: 0.5rem; min-width: 150px;">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 3 months</option>
                            <option value="365">Last year</option>
                        </select>
                        <button class="btn btn-secondary btn-sm" onclick="exportReport()">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                </div>

                <!-- Key Metrics -->
                <div class="dashboard-grid" style="margin-bottom: 2rem;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon revenue">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalRevenue">$0</h3>
                                <p>Total Revenue</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="revenueTrend">+0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalOrders">0</h3>
                                <p>Total Orders</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="ordersTrend">+0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="newCustomers">0</h3>
                                <p>New Customers</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="customersTrend">+0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="avgOrderValue">$0</h3>
                                <p>Avg Order Value</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="aovTrend">+0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <!-- Revenue Chart -->
                    <div class="data-table-container">
                        <div class="table-header">
                            <h3 class="table-title">Revenue Trend</h3>
                        </div>
                        <div style="padding: 2rem; height: 300px; display: flex; align-items: center; justify-content: center;">
                            <div id="revenueChart" style="width: 100%; height: 100%; background: var(--section-bg); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: var(--text-light);">
                                <div style="text-align: center;">
                                    <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                                    <p>Revenue chart would be displayed here<br><small>Integration with Chart.js or similar library</small></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Categories -->
                    <div class="data-table-container">
                        <div class="table-header">
                            <h3 class="table-title">Top Categories</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div id="topCategories">
                                <!-- Categories will be loaded dynamically -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Tables -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <!-- Top Products -->
                    <div class="data-table-container">
                        <div class="table-header">
                            <h3 class="table-title">Top Selling Products</h3>
                        </div>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Sales</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody id="topProductsTable">
                                <!-- Top products will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Recent Activity -->
                    <div class="data-table-container">
                        <div class="table-header">
                            <h3 class="table-title">Recent Activity</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div id="recentActivity">
                                <!-- Recent activity will be loaded dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Dropdown Styles -->
    <style>
        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }

        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .category-item:last-child {
            border-bottom: none;
        }

        .category-bar {
            width: 60px;
            height: 8px;
            background: var(--section-bg);
            border-radius: 4px;
            overflow: hidden;
        }

        .category-progress {
            height: 100%;
            background: var(--primary-color);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            color: white;
        }

        .activity-icon.order { background: var(--primary-color); }
        .activity-icon.user { background: var(--success-color); }
        .activity-icon.product { background: var(--warning-color); }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin access
            if (!authManager.requireAdmin()) {
                return;
            }

            // Initialize analytics
            initializeAnalytics();
            setupEventListeners();
        });

        function initializeAnalytics() {
            loadAnalyticsData();
        }

        function loadAnalyticsData() {
            const analytics = adminManager.getAnalytics();
            
            // Update key metrics
            document.getElementById('totalRevenue').textContent = formatCurrency(analytics.revenue.total);
            document.getElementById('revenueTrend').textContent = `+${analytics.revenue.growth.toFixed(1)}%`;
            
            document.getElementById('totalOrders').textContent = analytics.orders.total;
            document.getElementById('ordersTrend').textContent = `+${analytics.orders.growth.toFixed(1)}%`;
            
            document.getElementById('newCustomers').textContent = analytics.customers.new;
            document.getElementById('customersTrend').textContent = `+${analytics.customers.growth.toFixed(1)}%`;
            
            const avgOrderValue = analytics.revenue.total / analytics.orders.total;
            document.getElementById('avgOrderValue').textContent = formatCurrency(avgOrderValue);
            document.getElementById('aovTrend').textContent = '****%'; // Mock data
            
            // Load top categories
            loadTopCategories(analytics.salesByCategory);
            
            // Load top products
            loadTopProducts(analytics.topProducts);
            
            // Load recent activity
            loadRecentActivity();
        }

        function loadTopCategories(salesByCategory) {
            const total = Object.values(salesByCategory).reduce((sum, value) => sum + value, 0);
            const container = document.getElementById('topCategories');
            
            container.innerHTML = Object.entries(salesByCategory).map(([category, sales]) => {
                const percentage = (sales / total) * 100;
                return `
                    <div class="category-item">
                        <div>
                            <div style="font-weight: 500; text-transform: capitalize;">${category}</div>
                            <div style="font-size: 0.875rem; color: var(--text-light);">${formatCurrency(sales)}</div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; margin-bottom: 0.25rem;">${percentage.toFixed(1)}%</div>
                            <div class="category-bar">
                                <div class="category-progress" style="width: ${percentage}%;"></div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function loadTopProducts(topProducts) {
            const tbody = document.getElementById('topProductsTable');
            
            tbody.innerHTML = topProducts.map(product => `
                <tr>
                    <td style="font-weight: 500;">${product.name}</td>
                    <td>${product.sales}</td>
                    <td style="color: var(--primary-color); font-weight: 500;">${formatCurrency(product.revenue)}</td>
                </tr>
            `).join('');
        }

        function loadRecentActivity() {
            const activities = [
                {
                    type: 'order',
                    icon: 'shopping-cart',
                    title: 'New Order',
                    description: 'Order #ORD-003 placed',
                    time: '2 minutes ago'
                },
                {
                    type: 'user',
                    icon: 'user-plus',
                    title: 'New Customer',
                    description: 'Sarah Johnson registered',
                    time: '15 minutes ago'
                },
                {
                    type: 'product',
                    icon: 'box',
                    title: 'Low Stock Alert',
                    description: 'Premium T-Shirt running low',
                    time: '1 hour ago'
                },
                {
                    type: 'order',
                    icon: 'truck',
                    title: 'Order Shipped',
                    description: 'Order #ORD-001 shipped',
                    time: '2 hours ago'
                }
            ];

            const container = document.getElementById('recentActivity');
            container.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">
                        <i class="fas fa-${activity.icon}"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 500; margin-bottom: 0.25rem;">${activity.title}</div>
                        <div style="font-size: 0.875rem; color: var(--text-light);">${activity.description}</div>
                    </div>
                    <div style="font-size: 0.75rem; color: var(--text-light);">
                        ${activity.time}
                    </div>
                </div>
            `).join('');
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');
                
                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');
            });

            // Time period selector
            document.getElementById('timePeriod').addEventListener('change', function() {
                // In a real app, this would reload data for the selected period
                console.log('Time period changed to:', this.value, 'days');
            });

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                authManager.logout();
            });
        }

        function exportReport() {
            // Create mock report data
            const analytics = adminManager.getAnalytics();
            const reportData = {
                generatedAt: new Date().toISOString(),
                period: document.getElementById('timePeriod').value + ' days',
                summary: {
                    totalRevenue: analytics.revenue.total,
                    totalOrders: analytics.orders.total,
                    newCustomers: analytics.customers.new,
                    avgOrderValue: analytics.revenue.total / analytics.orders.total
                },
                topProducts: analytics.topProducts,
                salesByCategory: analytics.salesByCategory
            };
            
            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `vaith-analytics-report-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            // Show success message
            alert('Analytics report exported successfully!');
        }
    </script>
</body>
</html>
