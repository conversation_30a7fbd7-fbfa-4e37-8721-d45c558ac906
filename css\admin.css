/* Admin Dashboard Styles */

/* Admin Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background: var(--background-color);
}

.admin-sidebar {
    width: var(--admin-sidebar-width);
    background: var(--card-bg);
    border-right: 1px solid var(--border-color);
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--admin-transition);
    overflow-y: auto;
}

.admin-sidebar.collapsed {
    width: var(--admin-sidebar-collapsed);
}

.admin-main {
    flex: 1;
    margin-left: var(--admin-sidebar-width);
    transition: var(--admin-transition);
}

.admin-main.expanded {
    margin-left: var(--admin-sidebar-collapsed);
}

/* Admin Header */
.admin-header {
    height: var(--admin-header-height);
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 999;
}

.admin-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: var(--admin-transition);
}

.sidebar-toggle:hover {
    background: var(--section-bg);
}

.admin-header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Sidebar Navigation */
.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--admin-transition);
    border-radius: 0.5rem;
    margin: 0 0.5rem;
}

.nav-link:hover {
    background: var(--section-bg);
    color: var(--primary-color);
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
}

.nav-icon {
    width: 1.25rem;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-text {
    transition: var(--admin-transition);
}

.admin-sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    margin: 0.25rem;
}

.admin-sidebar.collapsed .nav-icon {
    margin-right: 0;
}

/* Admin Content */
.admin-content {
    padding: 2rem;
    min-height: calc(100vh - var(--admin-header-height));
}

.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: var(--text-light);
    font-size: 1rem;
}

/* Dashboard Cards */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--admin-card-shadow);
    transition: var(--admin-transition);
}

.dashboard-card:hover {
    box-shadow: var(--admin-card-hover-shadow);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
}

.card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-right: 1rem;
}

.card-icon.users { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.card-icon.orders { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.card-icon.revenue { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.card-icon.products { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.card-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.card-content p {
    color: var(--text-light);
    font-size: 0.875rem;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.trend-up { color: var(--status-active); }
.trend-down { color: var(--status-suspended); }

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active { background: rgba(16, 185, 129, 0.1); color: var(--status-active); }
.status-inactive { background: rgba(107, 114, 128, 0.1); color: var(--status-inactive); }
.status-pending { background: rgba(245, 158, 11, 0.1); color: var(--status-pending); }
.status-suspended { background: rgba(239, 68, 68, 0.1); color: var(--status-suspended); }
.status-completed { background: rgba(5, 150, 105, 0.1); color: var(--status-completed); }
.status-cancelled { background: rgba(220, 38, 38, 0.1); color: var(--status-cancelled); }

/* Data Tables */
.data-table-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--admin-card-shadow);
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.table-actions {
    display: flex;
    gap: 0.75rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--section-bg);
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    color: var(--text-color);
}

.data-table tbody tr:hover {
    background: var(--section-bg);
}

/* Action Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--admin-transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #6a1b9a;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--section-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

.btn-danger {
    background: var(--status-suspended);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 0.875rem;
    transition: var(--admin-transition);
}

.form-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
}

.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-content {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Enhanced Tooltips */
[title] {
    position: relative;
    cursor: pointer;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    margin-bottom: 0.25rem;
    animation: tooltipFadeIn 0.2s ease;
}

[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    pointer-events: none;
    margin-bottom: -4px;
    animation: tooltipFadeIn 0.2s ease;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}
