<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - VAITH Admin</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link active">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">Product Management</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Page Header -->
                <div class="page-header">
                    <h2 class="page-title">Products</h2>
                    <p class="page-subtitle">Manage your product catalog and inventory</p>
                </div>

                <!-- Product Stats -->
                <div class="dashboard-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin-bottom: 2rem;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalProductsCount">0</h3>
                                <p>Total Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="activeProductsCount">0</h3>
                                <p>Active Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="outOfStockCount">0</h3>
                                <p>Out of Stock</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="lowStockCount">0</h3>
                                <p>Low Stock</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">All Products</h3>
                        <div class="table-actions">
                            <div class="search-box" style="margin-right: 1rem;">
                                <input type="text" id="productSearch" placeholder="Search products..." style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem; background: var(--input-bg); color: var(--text-color);">
                            </div>
                            <select id="categoryFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Categories</option>
                                <option value="men">Men</option>
                                <option value="women">Women</option>
                            </select>
                            <select id="statusFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="draft">Draft</option>
                            </select>
                            <button class="btn btn-primary btn-sm" id="addProductBtn" onclick="openAddProductModal()" title="Add New Product">
                                <i class="fas fa-plus"></i> Add Product
                            </button>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>SKU</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Rating</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Products will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Product Modal (View/Add/Edit) -->
    <div class="modal" id="productModal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="modalTitle">Product Details</h3>
                <button class="close-modal" id="closeProductModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="productModalBody">
                <!-- Product details or form will be loaded here -->
            </div>
            <div class="modal-footer" id="modalFooter" style="display: none;">
                <!-- Modal action buttons will be added here -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" style="display: none;"></div>

    <!-- Modal and Dropdown Styles -->
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            max-height: 90vh;
            overflow-y: auto;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-color);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            background: var(--section-bg);
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
        }

        /* Form Styles */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Notification Animations */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }



        .stock-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stock-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .stock-high { background: var(--success-color); }
        .stock-medium { background: var(--warning-color); }
        .stock-low { background: var(--error-color); }
        .stock-out { background: var(--text-light); }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/validation.js"></script>

    <script>
        let currentProducts = [];
        let filteredProducts = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Check admin access
            if (!authManager.requireAdmin()) {
                return;
            }

            // Initialize page
            initializeProductsPage();
            setupEventListeners();
        });

        function initializeProductsPage() {
            loadProductStats();
            loadProducts();
        }

        function loadProductStats() {
            const stats = adminManager.getProductStats();
            document.getElementById('totalProductsCount').textContent = stats.total;
            document.getElementById('activeProductsCount').textContent = stats.active;
            document.getElementById('outOfStockCount').textContent = stats.outOfStock;
            document.getElementById('lowStockCount').textContent = stats.lowStock;
        }

        function loadProducts() {
            currentProducts = adminManager.getAllProducts();
            filteredProducts = [...currentProducts];
            renderProductsTable();
        }

        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');
            
            if (filteredProducts.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 2rem; color: var(--text-light);">No products found</td></tr>';
                return;
            }

            tbody.innerHTML = filteredProducts.map(product => `
                <tr>
                    <td>
                        <div>
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">${product.name}</div>
                            <div style="font-size: 0.75rem; color: var(--text-light);">${product.brand}</div>
                        </div>
                    </td>
                    <td style="font-family: monospace;">${product.sku}</td>
                    <td><span class="status-badge status-active">${product.category}</span></td>
                    <td>
                        <div style="font-weight: 500;">$${product.price}</div>
                        ${product.originalPrice && product.originalPrice !== product.price ? 
                            `<div style="font-size: 0.75rem; color: var(--text-light); text-decoration: line-through;">$${product.originalPrice}</div>` : 
                            ''
                        }
                    </td>
                    <td>
                        <div class="stock-indicator">
                            <div class="stock-dot ${getStockIndicatorClass(product.stock)}"></div>
                            <span>${product.stock}</span>
                        </div>
                    </td>
                    <td><span class="status-badge ${getProductStatusBadgeClass(product.status)}">${product.status}</span></td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 0.25rem;">
                            <span style="color: #fbbf24;">★</span>
                            <span>${product.rating.toFixed(1)}</span>
                            <span style="color: var(--text-light); font-size: 0.75rem;">(${product.reviews})</span>
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-secondary btn-sm" onclick="viewProduct(${product.id})" title="View Product Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="editProduct(${product.id})" title="Edit Product">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})" title="Delete Product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');
                
                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');
            });

            // Search functionality
            document.getElementById('productSearch').addEventListener('input', applyFilters);

            // Filter functionality
            document.getElementById('categoryFilter').addEventListener('change', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Modal close
            document.getElementById('closeProductModal').addEventListener('click', closeModal);
            document.getElementById('overlay').addEventListener('click', closeModal);

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                authManager.logout();
            });
        }

        function applyFilters() {
            const searchQuery = document.getElementById('productSearch').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredProducts = currentProducts.filter(product => {
                const matchesSearch = !searchQuery || 
                    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.brand.toLowerCase().includes(searchQuery.toLowerCase());

                const matchesCategory = !categoryFilter || product.category === categoryFilter;
                const matchesStatus = !statusFilter || product.status === statusFilter;

                return matchesSearch && matchesCategory && matchesStatus;
            });

            renderProductsTable();
        }

        function getStockIndicatorClass(stock) {
            if (stock === 0) return 'stock-out';
            if (stock <= 5) return 'stock-low';
            if (stock <= 20) return 'stock-medium';
            return 'stock-high';
        }

        function openAddProductModal() {
            document.getElementById('modalTitle').textContent = 'Add New Product';
            document.getElementById('productModalBody').innerHTML = createProductForm();
            document.getElementById('modalFooter').style.display = 'flex';
            document.getElementById('modalFooter').innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitProductForm()">Add Product</button>
            `;

            // Add form validation
            const form = document.getElementById('productForm');
            if (form) {
                addFormValidation(form);
            }

            showModal();
        }

        function createProductForm() {
            return `
                <form id="productForm" class="product-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="productName">Product Name *</label>
                            <input type="text" id="productName" name="name" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="productSku">SKU *</label>
                            <input type="text" id="productSku" name="sku" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="productDescription">Description *</label>
                        <textarea id="productDescription" name="description" class="form-input" rows="3" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="productPrice">Price ($) *</label>
                            <input type="number" id="productPrice" name="price" class="form-input" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="productOriginalPrice">Original Price ($)</label>
                            <input type="number" id="productOriginalPrice" name="originalPrice" class="form-input" step="0.01" min="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="productCategory">Category *</label>
                            <select id="productCategory" name="category" class="form-select" required>
                                <option value="">Select Category</option>
                                <option value="men">Men</option>
                                <option value="women">Women</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="productSubcategory">Subcategory *</label>
                            <input type="text" id="productSubcategory" name="subcategory" class="form-input" required placeholder="e.g., tops, bottoms, shoes">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="productBrand">Brand *</label>
                            <input type="text" id="productBrand" name="brand" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="productStock">Stock Quantity *</label>
                            <input type="number" id="productStock" name="stock" class="form-input" min="0" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="productStatus">Status</label>
                        <select id="productStatus" name="status" class="form-select">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="draft">Draft</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="productImages">Product Images (URLs) *</label>
                        <textarea id="productImages" name="images" class="form-input" rows="3" required placeholder="Enter image URLs, one per line"></textarea>
                        <small style="color: var(--text-light); font-size: 0.8rem;">Enter one image URL per line. First image will be the main product image.</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="productSizes">Available Sizes</label>
                            <input type="text" id="productSizes" name="sizes" class="form-input" placeholder="e.g., S, M, L, XL or 28, 30, 32, 34">
                            <small style="color: var(--text-light); font-size: 0.8rem;">Separate sizes with commas</small>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="productColors">Available Colors</label>
                            <input type="text" id="productColors" name="colors" class="form-input" placeholder="e.g., Black, White, Navy">
                            <small style="color: var(--text-light); font-size: 0.8rem;">Separate colors with commas</small>
                        </div>
                    </div>
                </form>
            `;
        }

        function viewProduct(productId) {
            const product = adminManager.getProductById(productId);
            if (!product) return;

            document.getElementById('modalTitle').textContent = 'Product Details';
            document.getElementById('modalFooter').style.display = 'none';
            document.getElementById('productModalBody').innerHTML = `
                <div style="display: grid; gap: 1.5rem;">
                    <div>
                        <h3 style="margin-bottom: 0.5rem;">${product.name}</h3>
                        <p style="color: var(--text-light); margin-bottom: 1rem;">${product.description}</p>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div><strong>Brand:</strong> ${product.brand}</div>
                            <div><strong>SKU:</strong> ${product.sku}</div>
                            <div><strong>Category:</strong> ${product.category}</div>
                            <div><strong>Status:</strong> <span class="status-badge ${getProductStatusBadgeClass(product.status)}">${product.status}</span></div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                        <div>
                            <strong>Price:</strong><br>
                            <span style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">$${product.price}</span>
                            ${product.originalPrice && product.originalPrice !== product.price ? 
                                `<span style="text-decoration: line-through; color: var(--text-light); margin-left: 0.5rem;">$${product.originalPrice}</span>` : 
                                ''
                            }
                        </div>
                        <div>
                            <strong>Stock:</strong><br>
                            <div class="stock-indicator" style="margin-top: 0.25rem;">
                                <div class="stock-dot ${getStockIndicatorClass(product.stock)}"></div>
                                <span>${product.stock} units</span>
                            </div>
                        </div>
                        <div>
                            <strong>Rating:</strong><br>
                            <div style="display: flex; align-items: center; gap: 0.25rem; margin-top: 0.25rem;">
                                <span style="color: #fbbf24;">★</span>
                                <span>${product.rating.toFixed(1)}</span>
                                <span style="color: var(--text-light);">(${product.reviews} reviews)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <strong>Available Sizes:</strong><br>
                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${product.sizes.map(size => `<span class="status-badge status-active">${size}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Available Colors:</strong><br>
                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${product.colors.map(color => `<span class="status-badge status-pending">${color}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Created:</strong> ${formatDate(product.createdDate)}<br>
                        <strong>Last Updated:</strong> ${formatDate(product.updatedDate)}
                    </div>
                </div>
            `;
            
            showModal();
        }

        function submitProductForm() {
            const form = document.getElementById('productForm');
            if (!form) return;

            // Validate form
            if (!validateProductForm(form)) {
                return;
            }

            // Collect form data
            const formData = new FormData(form);
            const productData = {};

            // Process basic fields
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    productData[key] = value.trim();
                }
            }

            // Process numeric fields
            if (productData.price) productData.price = parseFloat(productData.price);
            if (productData.originalPrice) productData.originalPrice = parseFloat(productData.originalPrice);
            if (productData.stock) productData.stock = parseInt(productData.stock);

            // Process arrays
            if (productData.images) {
                productData.images = productData.images.split('\n')
                    .map(url => url.trim())
                    .filter(url => url.length > 0);
            }

            if (productData.sizes) {
                productData.sizes = productData.sizes.split(',')
                    .map(size => size.trim())
                    .filter(size => size.length > 0);
            } else {
                productData.sizes = [];
            }

            if (productData.colors) {
                productData.colors = productData.colors.split(',')
                    .map(color => color.trim())
                    .filter(color => color.length > 0);
            } else {
                productData.colors = [];
            }

            // Set default status if not provided
            if (!productData.status) {
                productData.status = 'active';
            }

            try {
                // Create the product
                const newProduct = adminManager.createProduct(productData);

                if (newProduct) {
                    // Show success message
                    showNotification('Product added successfully!', 'success');

                    // Refresh the products table and stats
                    loadProducts();
                    loadProductStats();

                    // Close modal
                    closeModal();
                } else {
                    showNotification('Failed to add product. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Error adding product:', error);
                showNotification('An error occurred while adding the product.', 'error');
            }
        }

        function validateProductForm(form) {
            let isValid = true;
            const errors = [];

            // Get form values
            const name = form.querySelector('#productName').value.trim();
            const sku = form.querySelector('#productSku').value.trim();
            const description = form.querySelector('#productDescription').value.trim();
            const price = form.querySelector('#productPrice').value.trim();
            const category = form.querySelector('#productCategory').value.trim();
            const subcategory = form.querySelector('#productSubcategory').value.trim();
            const brand = form.querySelector('#productBrand').value.trim();
            const stock = form.querySelector('#productStock').value.trim();
            const images = form.querySelector('#productImages').value.trim();

            // Validate required fields
            if (!name) {
                showFieldError(form.querySelector('#productName'), 'Product name is required');
                isValid = false;
            }

            if (!sku) {
                showFieldError(form.querySelector('#productSku'), 'SKU is required');
                isValid = false;
            } else {
                // Check SKU uniqueness
                const existingProduct = adminManager.getAllProducts().find(p => p.sku === sku);
                if (existingProduct) {
                    showFieldError(form.querySelector('#productSku'), 'SKU already exists');
                    isValid = false;
                }
            }

            if (!description) {
                showFieldError(form.querySelector('#productDescription'), 'Description is required');
                isValid = false;
            }

            if (!price) {
                showFieldError(form.querySelector('#productPrice'), 'Price is required');
                isValid = false;
            } else if (parseFloat(price) <= 0) {
                showFieldError(form.querySelector('#productPrice'), 'Price must be greater than 0');
                isValid = false;
            }

            if (!category) {
                showFieldError(form.querySelector('#productCategory'), 'Category is required');
                isValid = false;
            }

            if (!subcategory) {
                showFieldError(form.querySelector('#productSubcategory'), 'Subcategory is required');
                isValid = false;
            }

            if (!brand) {
                showFieldError(form.querySelector('#productBrand'), 'Brand is required');
                isValid = false;
            }

            if (!stock) {
                showFieldError(form.querySelector('#productStock'), 'Stock quantity is required');
                isValid = false;
            } else if (parseInt(stock) < 0) {
                showFieldError(form.querySelector('#productStock'), 'Stock quantity cannot be negative');
                isValid = false;
            }

            if (!images) {
                showFieldError(form.querySelector('#productImages'), 'At least one product image is required');
                isValid = false;
            } else {
                // Validate image URLs
                const imageUrls = images.split('\n').map(url => url.trim()).filter(url => url.length > 0);
                const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i;

                for (let url of imageUrls) {
                    if (!urlPattern.test(url)) {
                        showFieldError(form.querySelector('#productImages'), 'Please enter valid image URLs (jpg, jpeg, png, gif, webp)');
                        isValid = false;
                        break;
                    }
                }
            }

            return isValid;
        }

        function editProduct(productId) {
            // For now, just show view - in a real app, this would open an edit form
            viewProduct(productId);
        }

        function deleteProduct(productId) {
            const product = adminManager.getProductById(productId);
            if (!product) return;

            if (confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
                adminManager.deleteProduct(productId);
                loadProducts();
                loadProductStats();
            }
        }

        function showModal() {
            document.getElementById('productModal').style.display = 'flex';
            document.getElementById('overlay').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
        }

        // Helper functions for form validation and notifications
        function showFieldError(field, message) {
            const fieldGroup = field.closest('.form-group') || field.parentElement;
            let errorElement = fieldGroup.querySelector('.error-message');

            // Create error element if it doesn't exist
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'error-message';
                errorElement.style.color = 'var(--error-color)';
                errorElement.style.fontSize = '0.75rem';
                errorElement.style.marginTop = '0.25rem';
                fieldGroup.appendChild(errorElement);
            }

            // Add error styling
            field.classList.add('error');
            field.style.borderColor = 'var(--error-color)';
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        function clearFieldError(field) {
            const fieldGroup = field.closest('.form-group') || field.parentElement;
            const errorElement = fieldGroup.querySelector('.error-message');

            if (errorElement) {
                errorElement.style.display = 'none';
            }

            field.classList.remove('error');
            field.style.borderColor = '';
        }

        function addFormValidation(form) {
            const inputs = form.querySelectorAll('input, textarea, select');

            inputs.forEach(input => {
                // Clear errors on input
                input.addEventListener('input', () => clearFieldError(input));
                input.addEventListener('change', () => clearFieldError(input));
            });
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease;
            `;

            // Set background color based on type
            switch (type) {
                case 'success':
                    notification.style.backgroundColor = 'var(--success-color)';
                    break;
                case 'error':
                    notification.style.backgroundColor = 'var(--error-color)';
                    break;
                case 'warning':
                    notification.style.backgroundColor = 'var(--warning-color)';
                    break;
                default:
                    notification.style.backgroundColor = 'var(--primary-color)';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>
